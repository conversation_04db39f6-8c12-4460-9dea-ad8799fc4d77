from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from .models import Lake, LakePhoto, County
from decimal import Decimal
import tempfile
from PIL import Image
import io


class LakePhotoGalleryTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create a test county
        self.county = County.objects.create(
            name="Test County",
            slug="test-county",
            region="Test Region"
        )

        # Create a test lake
        self.lake = Lake.objects.create(
            name="Test Lake",
            slug="test-lake",
            description="A test lake for testing",
            address="Test Address",
            county=self.county,
            latitude=Decimal('45.123456'),
            longitude=Decimal('25.123456'),
            lake_type='private',
            price_per_day=Decimal('50.00')
        )

    def create_test_image(self):
        """Create a test image file"""
        image = Image.new('RGB', (100, 100), color='red')
        image_file = io.BytesIO()
        image.save(image_file, 'JPEG')
        image_file.seek(0)
        return SimpleUploadedFile(
            name='test_image.jpg',
            content=image_file.read(),
            content_type='image/jpeg'
        )

    def test_lake_main_image_fallback_logic(self):
        """Test the cascading image priority logic"""
        # Test 1: No images at all - should return None
        self.assertIsNone(self.lake.get_main_image())
        self.assertIsNone(self.lake.main_image_url)

        # Test 2: Only legacy image - should return legacy image
        test_image = self.create_test_image()
        self.lake.image = test_image
        self.lake.save()

        main_image = self.lake.get_main_image()
        self.assertIsNotNone(main_image)
        self.assertEqual(main_image, self.lake.image)

        # Test 3: Add gallery photo - should prioritize gallery
        gallery_image = self.create_test_image()
        photo = LakePhoto.objects.create(
            lake=self.lake,
            image=gallery_image,
            order=0,
            is_main=False
        )

        main_image = self.lake.get_main_image()
        self.assertEqual(main_image, photo.image)

        # Test 4: Add main gallery photo - should prioritize main photo
        main_gallery_image = self.create_test_image()
        main_photo = LakePhoto.objects.create(
            lake=self.lake,
            image=main_gallery_image,
            order=1,
            is_main=True
        )

        main_image = self.lake.get_main_image()
        self.assertEqual(main_image, main_photo.image)

    def test_solunar_calendar_button_on_homepage(self):
        """Test that the solunar calendar button appears on homepage"""
        response = self.client.get(reverse('main:home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Vezi Calendarul Solunar')
        self.assertContains(response, reverse('main:solunar_calendar'))

    def test_solunar_calendar_page(self):
        """Test that the solunar calendar page loads correctly"""
        response = self.client.get(reverse('main:solunar_calendar'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Calendar Solunar')

    def test_lake_detail_gallery_display(self):
        """Test that lake detail page displays gallery correctly"""
        # Add some photos to the lake
        for i in range(3):
            photo = LakePhoto.objects.create(
                lake=self.lake,
                image=self.create_test_image(),
                order=i,
                is_main=(i == 1)  # Make second photo the main one
            )

        response = self.client.get(reverse('main:lake_detail', kwargs={'slug': self.lake.slug}))
        self.assertEqual(response.status_code, 200)

        # Check that gallery elements are present
        self.assertContains(response, 'lake-gallery')
        self.assertContains(response, 'thumbnail-strip')
        self.assertContains(response, 'gallery-nav')
        self.assertContains(response, '1 / 3')  # Photo counter

    def test_lake_cards_use_new_image_logic(self):
        """Test that lake cards use the new image display logic"""
        # Add a gallery photo
        photo = LakePhoto.objects.create(
            lake=self.lake,
            image=self.create_test_image(),
            order=0,
            is_main=True
        )

        # Test homepage
        response = self.client.get(reverse('main:home'))
        self.assertEqual(response.status_code, 200)

        # Test county lakes page
        response = self.client.get(reverse('main:county_lakes', kwargs={'county_slug': self.county.slug}))
        self.assertEqual(response.status_code, 200)
