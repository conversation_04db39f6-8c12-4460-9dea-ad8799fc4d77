{% extends 'base.html' %}
{% load static %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-image">
        <img src="{% static 'images/hero.png' %}" alt="Hero Background">
    </div>
    <div class="hero-overlay"></div>
    <div class="hero-text">
        <h1>Bine ai venit la Răsfățul Pescarului</h1>
        <p>Descoperă cele mai bune locuri de pescuit și echipamente de calitate pentru pasiunea ta!</p>
        <a href="{{ hero.main_button_url }}" class="btn btn-success btn-lg mt-4">{{ hero.main_button_text }}</a>
        <div class="social-icons mt-3">
            <a href="{{ hero.facebook_url }}" class="social-icon" style="color: #198653; margin: 0 10px;">
                <i class="fab fa-facebook fa-2x"></i>
            </a>
            <a href="{{ hero.tiktok_url }}" class="social-icon" style="color: #198653; margin: 0 10px;">
                <i class="fab fa-tiktok fa-2x"></i>
            </a>
        </div>
    </div>

    <!-- Valuri animate -->
    <div class="waves-container">
        <svg class="waves" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
             viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
            <defs>
                <path id="gentle-wave"
                      d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z"></path>
            </defs>
            <g class="parallax">
                <use xlink:href="#gentle-wave" x="48" y="0" fill="rgba(222, 244, 252,0.7)"></use>
                <use xlink:href="#gentle-wave" x="48" y="3" fill="rgba(222, 244, 252,0.5)"></use>
                <use xlink:href="#gentle-wave" x="48" y="5" fill="rgba(222, 244, 252,0.3)"></use>
                <use xlink:href="#gentle-wave" x="48" y="7" fill="#fff"></use>
            </g>
        </svg>
    </div>
</section>

<!-- Welcome Section -->
<section class="welcome-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-5 image-container">
                <img src="{% static 'images/logo.png' %}" alt="Răsfățul Pescarului Logo" class="img-fluid">
            </div>
            <div class="col-lg-7 text-container">
                <h2>Bine ați venit pe Răsfățul Pescarului</h2>
                <p class="lead">Platforma dedicată pasionaților de pescuit din toată țara! Aici, veți descoperi o resursă unică pentru iubitorii de pescuit: un serviciu inovator care vă permite să explorați toate speciile de pești și baltițele din România.</p>
            </div>
        </div>
    </div>
</section>

<!-- Explore Section -->
<section class="fishing-explore-section">
    <div class="container">
        <div class="fishing-explore-container">
            <div class="fishing-text-content">
                <h2>Explorați cu noi<br>lumea captivantă<br>a pescuitului.</h2>
                <p class="mt-4">Fie că sunteți în căutarea unui loc de pescuit perfect sau doriți să aflați mai multe despre speciile de pești din apele noastre, Răsfățul Pescarului vă oferă toate informațiile necesare pentru a vă planifica o experiență de pescuit de neuitat. Alăturați-vă comunității noastre și începeți aventura pe ape!</p>
            </div>
            <div class="fishing-image-container">
                <img src="{% static 'images/img_4.png' %}" alt="Peisaj de pescuit" class="img-fluid">
            </div>
        </div>
    </div>
</section>

<!-- Random Lakes Section -->
<section class="random-lakes py-5">
    <div class="container">
        <h2 class="text-center mb-4">Bălți recomandate</h2>
        <p class="text-center mb-5">Descoperă cele mai interesante locuri de pescuit din România</p>

        {% if random_lakes %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for lake in random_lakes %}
            <div class="col">
                <div class="card h-100 d-flex flex-column">
                    <!-- Card Image - Fixed Height -->
                    {% if lake.main_image_url %}
                    <img src="{{ lake.main_image_url }}" class="card-img-top" alt="{{ lake.name }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                    <img src="{% static 'images/lake-placeholder.jpg' %}" class="card-img-top" alt="{{ lake.name }}" style="height: 200px; object-fit: cover;">
                    {% endif %}

                    <!-- Card Body - Flexible Content -->
                    <div class="card-body d-flex flex-column">
                        <!-- Title - Fixed Height -->
                        <h5 class="card-title mb-3" style="min-height: 2.5rem; line-height: 1.25;">{{ lake.name }}</h5>

                        <!-- Description - Fixed Height -->
                        <p class="card-text mb-3" style="min-height: 4.5rem; line-height: 1.5;">
                            {% if lake.description %}
                                {{ lake.description|truncatechars:100 }}
                            {% else %}
                                <span class="text-muted">Descriere indisponibilă</span>
                            {% endif %}
                        </p>

                        <!-- Rating Section - Fixed Height -->
                        <div class="mb-3" style="min-height: 1.5rem;">
                            {% if lake.average_rating %}
                            <div class="d-flex align-items-center gap-1">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= lake.average_rating %}
                                        <i class="fas fa-star" style="color: #ffc107;"></i>
                                    {% else %}
                                        <i class="far fa-star" style="color: #e9ecef;"></i>
                                    {% endif %}
                                {% endfor %}
                                <span class="ms-1 fw-bold">{{ lake.average_rating|floatformat:1 }}</span>
                                <small class="text-muted">({{ lake.total_reviews }} recenzii)</small>
                            </div>
                            {% else %}
                            <div class="text-muted">
                                <small>Fără recenzii încă</small>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Lake Details - Fixed Structure -->
                        <ul class="list-unstyled mb-4 flex-grow-1">
                            <li class="mb-2" style="min-height: 1.5rem;">
                                <i class="fas fa-fish me-2 text-success"></i>
                                {% if lake.fish_species.all %}
                                    {% for fish in lake.fish_species.all %}{{ fish.name }}{% if not forloop.last %}, {% endif %}{% endfor %}
                                {% else %}
                                    <span class="text-muted">Informații indisponibile</span>
                                {% endif %}
                            </li>
                            <li class="mb-2" style="min-height: 1.5rem;">
                                <i class="fas fa-coins me-2 text-warning"></i>
                                {{ lake.price_per_day }} Lei/zi
                            </li>
                            <li class="mb-2" style="min-height: 1.5rem;">
                                <i class="fas fa-map-marker-alt me-2 text-danger"></i>
                                {{ lake.address }}
                            </li>
                        </ul>
                    </div>

                    <!-- Card Footer - Fixed Position at Bottom -->
                    <div class="card-footer bg-transparent border-0 pt-0">
                        <div class="d-grid">
                            <a href="{% url 'main:lake_detail' lake.slug %}" class="btn btn-success">
                                Vezi detalii
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="text-center mt-4">
            <a href="{% url 'main:fishing_locations' %}" class="btn btn-outline-success btn-lg">
                Vezi toate băltile
            </a>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-water fa-3x text-muted mb-3"></i>
            <h3>Nu există bălți disponibile</h3>
            <p class="text-muted">Momentan nu avem bălți de pescuit înregistrate în sistem.</p>
        </div>
        {% endif %}
    </div>
</section>

<!-- Call to Action -->
<section class="cta py-5 bg-success text-white border-top">
    <div class="container text-center">
        <h2 class="mb-4">Pregătit să începi aventura?</h2>
        <p class="lead mb-4">Alătură-te comunității noastre și descoperă cele mai bune locuri de pescuit!</p>
        <a href="https://www.facebook.com/rasfatulpescarului" class="btn btn-light btn-lg">Descoperă mai multe</a>
    </div>
</section>

<!-- Solunar Section -->
<section class="solunar-section py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-4">Calendar Solunar</h2>
        <p class="text-center mb-5">Află cele mai bune perioade pentru pescuit bazate pe fazele lunii</p>
        
        <div class="row">
            {% for prediction in solunar_predictions %}
            <div class="col-md-4 mb-4">
                <div class="card h-100 solunar-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="moon-phase-icon me-3">
                                {% if prediction.moon_phase < 0.125 %}
                                    <svg width="60" height="60" viewBox="0 0 60 60">
                                        <circle cx="30" cy="30" r="28" fill="#1a1a1a" stroke="#333" stroke-width="2"/>
                                    </svg>
                                {% elif prediction.moon_phase < 0.375 %}
                                    <svg width="60" height="60" viewBox="0 0 60 60">
                                        <circle cx="30" cy="30" r="28" fill="#f8f9fa" stroke="#333" stroke-width="2"/>
                                        <path d="M30 2A28 28 0 0 1 30 58A28 28 0 0 0 30 2" fill="#1a1a1a"/>
                                    </svg>
                                {% elif prediction.moon_phase < 0.625 %}
                                    <svg width="60" height="60" viewBox="0 0 60 60">
                                        <circle cx="30" cy="30" r="28" fill="#f8f9fa" stroke="#333" stroke-width="2"/>
                                    </svg>
                                {% elif prediction.moon_phase < 0.875 %}
                                    <svg width="60" height="60" viewBox="0 0 60 60">
                                        <circle cx="30" cy="30" r="28" fill="#f8f9fa" stroke="#333" stroke-width="2"/>
                                        <path d="M30 2A28 28 0 0 0 30 58A28 28 0 0 1 30 2" fill="#1a1a1a"/>
                                    </svg>
                                {% else %}
                                    <svg width="60" height="60" viewBox="0 0 60 60">
                                        <circle cx="30" cy="30" r="28" fill="#1a1a1a" stroke="#333" stroke-width="2"/>
                                    </svg>
                                {% endif %}
                            </div>
                            <div>
                                <h5 class="card-title mb-0">
                                    {% if forloop.first %}
                                        Solunar azi
                                    {% else %}
                                        {{ prediction.date|date:"l, j F" }}
                                    {% endif %}
                                </h5>
                                <div class="text-muted">Rating: {{ prediction.rating|floatformat:2 }}/5</div>
                            </div>
                        </div>
                        
                        <div class="fishing-times mb-3">
                            <h6 class="text-success">Orar pescuit favorabil:</h6>
                            <div class="d-flex justify-content-around">
                                <span>{{ prediction.major_start|time:"H:i" }}</span>
                                <span>{{ prediction.major_end|time:"H:i" }}</span>
                            </div>
                            
                            <h6 class="text-danger mt-3">Orar pescuit nefavorabil:</h6>
                            <div class="d-flex justify-content-around">
                                <span>{{ prediction.minor_start|time:"H:i" }}</span>
                                <span>{{ prediction.minor_end|time:"H:i" }}</span>
                            </div>
                        </div>
                        
                        <div class="fishing-rating text-center">
                            <div class="fish-icons">
                                {% with ''|center:5 as range %}
                                {% for _ in range %}
                                    <i class="fas fa-fish {% if forloop.counter <= prediction.rating %}text-primary{% else %}text-muted{% endif %}"></i>
                                {% endfor %}
                                {% endwith %}
                            </div>
                            <small class="text-muted">Șanse de pescuit: {{ prediction.rating_text }}</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Solunar Calendar Navigation Button -->
        <div class="text-center mt-4">
            <a href="{% url 'main:solunar_calendar' %}" class="btn btn-outline-success btn-lg">
                Vezi Calendarul Solunar
            </a>
        </div>
    </div>
</section>

<!-- County Selection Section -->
<section class="county-selection-section py-5">
    <div class="container">
        <h2 class="text-center mb-3">Selecteaza judetul</h2>
        <p class="text-center mb-5">Selecteaza judetul in care te afli pentru a vedea zonele de pescuit plus ce tipuri de pestii poti gasi in ele.</p>
        <div class="map-container text-center">
            <a href="{% url 'main:locations_map' %}" class="map-link">
                <img src="{% static 'images/romania.svg' %}" alt="Harta Romaniei" class="romania-map img-fluid">
            </a>
        </div>
    </div>
</section>

<!-- Featured Videos -->
<section class="featured-videos py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-4">Tutoriale recente</h2>
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for video in featured_videos %}
            <div class="col">
                <div class="card h-100">
                    <div class="ratio ratio-16x9">
                        <iframe src="{{ video.embed_url }}" title="{{ video.title }}" allowfullscreen></iframe>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ video.title }}</h5>
                        <p class="card-text">{{ video.description|truncatechars:100 }}</p>
                        <a href="{% url 'main:video_detail' video.id %}" class="btn btn-outline-success">
                            Vezi tutorialul
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{% url 'main:tutorials' %}" class="btn btn-outline-success btn-lg">
                Vezi toate tutorialele
            </a>
        </div>
    </div>
</section>

{% endblock %}
