# Generated by Django 5.1.6 on 2025-06-11 19:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0011_auto_20250605_2315'),
    ]

    operations = [
        migrations.CreateModel(
            name='LakePhoto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Imaginea pentru galeria lacului (format recomandat: JPG, PNG, max 2MB)', upload_to='lakes/gallery/', verbose_name='Imagine')),
                ('order', models.PositiveIntegerField(default=0, help_text='Ordinea în care va fi afișată imaginea în galerie (0 = prima)', verbose_name='Ordinea afișării')),
                ('is_main', models.BooleanField(default=False, help_text='Bifează pentru a seta această imagine ca imagine principală a lacului', verbose_name='Imagine principală')),
                ('caption', models.CharField(blank=True, help_text='Descrierea opțională a imaginii', max_length=200, verbose_name='Descriere')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
                ('lake', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='main.lake', verbose_name='Lac')),
            ],
            options={
                'verbose_name': 'Fotografie lac',
                'verbose_name_plural': 'Fotografii lac',
                'ordering': ['order', 'id'],
            },
        ),
    ]
