{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'main:home' %}">Acasă</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'main:fishing_locations' %}">Bălți de pescuit</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ county.name }}</li>
                </ol>
            </nav>
            <h1 class="mb-3">Bălți de pescuit în {{ county.name }}</h1>
            <p class="lead">Descoperă cele mai bune locuri de pescuit din județul {{ county.name }}</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <p class="mb-0">{{ lakes.count }} rezultate găsite</p>
                </div>
                <div>
                    <a href="{% url 'main:locations_map' %}" class="btn btn-outline-success">
                        <i class="fas fa-map-marker-alt me-2"></i>Vezi pe hartă
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {% for lake in lakes %}
        <div class="col">
            <div class="card h-100">
                {% if lake.main_image_url %}
                <img src="{{ lake.main_image_url }}" class="card-img-top" alt="{{ lake.name }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <img src="{% static 'images/lake-placeholder.jpg' %}" class="card-img-top" alt="{{ lake.name }}" style="height: 200px; object-fit: cover;">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ lake.name }}</h5>
                    <p class="card-text">{{ lake.description|truncatechars:100 }}</p>

                    <!-- Rating Display -->
                    {% if lake.total_reviews > 0 %}
                    <div class="mb-2">
                        <div class="d-flex align-items-center gap-2">
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= lake.average_rating %}
                                        <i class="fas fa-star" style="color: #ffc107;"></i>
                                    {% else %}
                                        <i class="far fa-star" style="color: #e9ecef;"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="fw-bold" style="color: #ffc107;">{{ lake.average_rating }}</span>
                            <small class="text-muted">({{ lake.total_reviews }})</small>
                        </div>
                    </div>
                    {% endif %}

                    <ul class="list-unstyled">
                        <li><i class="fas fa-fish me-2"></i>{% for fish in lake.fish_species.all %}{{ fish.name }}{% if not forloop.last %}, {% endif %}{% endfor %}</li>
                        <li><i class="fas fa-coins me-2"></i>{{ lake.price_per_day }} Lei/zi</li>
                        <li><i class="fas fa-map-marker-alt me-2"></i>{{ lake.address }}</li>
                    </ul>
                    <div class="d-grid">
                        <a href="{% url 'main:lake_detail' lake.slug %}" class="btn btn-success">
                            Vezi detalii
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                Nu există bălți de pescuit înregistrate în acest județ.
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}