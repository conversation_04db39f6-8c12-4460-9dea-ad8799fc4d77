# Generated by Django 5.0.1 on 2025-02-23 20:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='County',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('region', models.CharField(choices=[('MOLDOVA', 'Moldova'), ('MUNTENIA', 'Muntenia'), ('OLTENIA', 'Oltenia'), ('BANAT', 'Banat'), ('CRISANA', 'Crisana'), ('MARAMURES', 'Maramures'), ('TRANSILVANIA', 'Transilvania'), ('DOBROGEA', 'Dobrogea'), ('BUCURESTI', '<PERSON><PERSON><PERSON><PERSON>')], max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'counties',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='Răsfățul Pescarului', max_length=100)),
                ('contact_email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('phone', models.CharField(default='0700000000', max_length=20)),
                ('address', models.TextField(default='România')),
                ('facebook_url', models.URLField(blank=True)),
                ('instagram_url', models.URLField(blank=True)),
                ('youtube_url', models.URLField(blank=True)),
                ('about_text', models.TextField(default='Despre noi')),
            ],
            options={
                'verbose_name': 'Site Settings',
                'verbose_name_plural': 'Site Settings',
            },
        ),
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('url', models.URLField()),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to='videos/thumbnails/')),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Video',
                'verbose_name_plural': 'Videos',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Lake',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('address', models.CharField(max_length=255)),
                ('latitude', models.DecimalField(decimal_places=6, max_digits=9)),
                ('longitude', models.DecimalField(decimal_places=6, max_digits=9)),
                ('fish_types', models.CharField(max_length=500)),
                ('facilities', models.CharField(max_length=500)),
                ('price_per_day', models.DecimalField(decimal_places=2, max_digits=10)),
                ('image', models.ImageField(blank=True, null=True, upload_to='lakes/')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('county', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lakes', to='main.county')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
    ]
