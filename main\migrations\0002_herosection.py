# Generated by Django 5.0.1 on 2025-02-23 21:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='HeroSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('main_button_text', models.CharField(default='Alaturati-va grupului', max_length=100)),
                ('main_button_url', models.URLField(default='https://www.facebook.com/rasfatulpescarului')),
                ('facebook_url', models.URLField(default='https://www.facebook.com/rasfatulpescarului')),
                ('tiktok_url', models.URLField(default='https://www.tiktok.com/@rasfatulpescarului')),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Hero Section',
                'verbose_name_plural': 'Hero Section',
            },
        ),
    ]
