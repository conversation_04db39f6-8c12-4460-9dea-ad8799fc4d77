# Generated by Django 5.1.6 on 2025-06-05 19:50

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0008_auto_20250605_2215'),
    ]

    operations = [
        migrations.CreateModel(
            name='Facility',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Numele facilității')),
                ('icon_class', models.CharField(help_text='Ex: fas fa-parking, fas fa-bed', max_length=50, verbose_name='Clasa icon FontAwesome')),
                ('category', models.CharField(choices=[('basic', 'De bază'), ('accommodation', 'Cazare'), ('food', 'Mâncare și băutură'), ('fishing', 'Pescuit'), ('services', 'Servicii'), ('recreation', 'Recreere')], default='basic', max_length=20, verbose_name='Categoria')),
                ('description', models.TextField(blank=True, help_text='Descrierea detaliată a facilității', verbose_name='Descriere')),
                ('is_active', models.BooleanField(default=True, verbose_name='Activ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
            ],
            options={
                'verbose_name': 'Facilitate',
                'verbose_name_plural': 'Facilități',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FishSpecies',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Numele românesc al speciei de pește', max_length=100, verbose_name='Numele speciei')),
                ('scientific_name', models.CharField(blank=True, help_text='Numele latin al speciei (opțional)', max_length=150, verbose_name='Numele științific')),
                ('category', models.CharField(choices=[('cyprinid', 'Ciprinide'), ('predator', 'Prădători'), ('other', 'Alte specii')], default='other', help_text='Categoria biologică a peștelui', max_length=20, verbose_name='Categoria')),
                ('is_active', models.BooleanField(default=True, verbose_name='Activ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
            ],
            options={
                'verbose_name': 'Specie de pește',
                'verbose_name_plural': 'Specii de pești',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.RemoveField(
            model_name='lake',
            name='fish_types',
        ),
        migrations.RemoveField(
            model_name='lake',
            name='operating_hours',
        ),
        migrations.RemoveField(
            model_name='lake',
            name='facilities',
        ),
        migrations.AddField(
            model_name='lake',
            name='fish_species',
            field=models.ManyToManyField(blank=True, help_text='Selectează speciile de pești disponibile în acest lac', to='main.fishspecies', verbose_name='Specii de pești'),
        ),
        migrations.CreateModel(
            name='OperatingHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('monday_is_open', models.BooleanField(default=True, verbose_name='Deschis Luni')),
                ('monday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('monday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('monday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('monday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('tuesday_is_open', models.BooleanField(default=True, verbose_name='Deschis Marți')),
                ('tuesday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('tuesday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('tuesday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('tuesday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('wednesday_is_open', models.BooleanField(default=True, verbose_name='Deschis Miercuri')),
                ('wednesday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('wednesday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('wednesday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('wednesday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('thursday_is_open', models.BooleanField(default=True, verbose_name='Deschis Joi')),
                ('thursday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('thursday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('thursday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('thursday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('friday_is_open', models.BooleanField(default=True, verbose_name='Deschis Vineri')),
                ('friday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('friday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('friday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('friday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('saturday_is_open', models.BooleanField(default=True, verbose_name='Deschis Sâmbătă')),
                ('saturday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('saturday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('saturday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('saturday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('sunday_is_open', models.BooleanField(default=True, verbose_name='Deschis Duminică')),
                ('sunday_opening_time', models.TimeField(blank=True, null=True, verbose_name='Ora deschidere')),
                ('sunday_closing_time', models.TimeField(blank=True, null=True, verbose_name='Ora închidere')),
                ('sunday_is_24h', models.BooleanField(default=False, verbose_name='24 ore')),
                ('sunday_special_notes', models.CharField(blank=True, max_length=200, verbose_name='Note speciale')),
                ('general_notes', models.TextField(blank=True, help_text='Informații generale despre program', verbose_name='Note generale')),
                ('lake', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='operating_hours', to='main.lake', verbose_name='Lac')),
            ],
            options={
                'verbose_name': 'Program de funcționare',
                'verbose_name_plural': 'Programe de funcționare',
            },
        ),
        migrations.AddField(
            model_name='lake',
            name='facilities',
            field=models.ManyToManyField(blank=True, help_text='Selectează facilitățile disponibile la acest lac', to='main.facility', verbose_name='Facilități'),
        ),
        migrations.CreateModel(
            name='LakeReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reviewer_name', models.CharField(help_text='Numele dvs. complet', max_length=100, verbose_name='Numele recenzentului')),
                ('reviewer_email', models.EmailField(help_text='Pentru verificare (nu va fi afișat public)', max_length=254, verbose_name='Email')),
                ('rating', models.IntegerField(choices=[(1, '1 stea'), (2, '2 stele'), (3, '3 stele'), (4, '4 stele'), (5, '5 stele')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Rating')),
                ('title', models.CharField(help_text='Titlu scurt pentru experiența dvs.', max_length=200, verbose_name='Titlul recenziei')),
                ('comment', models.TextField(help_text='Descrieți experiența dvs. la acest lac (minim 20 caractere)', max_length=1000, validators=[django.core.validators.MinLengthValidator(20)], verbose_name='Comentariu')),
                ('visit_date', models.DateField(help_text='Când ați vizitat lacul', verbose_name='Data vizitei')),
                ('is_approved', models.BooleanField(default=False, verbose_name='Aprobat')),
                ('is_spam', models.BooleanField(default=False, verbose_name='Spam')),
                ('ip_address', models.GenericIPAddressField(verbose_name='Adresa IP')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Data creării')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Data actualizării')),
                ('lake', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='main.lake', verbose_name='Lac')),
            ],
            options={
                'verbose_name': 'Recenzie lac',
                'verbose_name_plural': 'Recenzii lacuri',
                'ordering': ['-created_at'],
                'unique_together': {('lake', 'reviewer_email')},
            },
        ),
    ]
