# Generated by Django 5.0.1 on 2025-02-23 21:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0002_herosection'),
    ]

    operations = [
        migrations.CreateModel(
            name='FooterSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contact_info', models.CharField(default='Contact', max_length=200)),
                ('address', models.TextField(default='Strada Exemplu, Nr. 123, București')),
                ('phone', models.<PERSON><PERSON><PERSON><PERSON>(default='+40 123 456 789', max_length=20)),
                ('email', models.EmailField(default='<EMAIL>', max_length=254)),
                ('working_hours', models.<PERSON>r<PERSON>ield(default='Luni - Vineri: 09:00 - 18:00', max_length=100)),
            ],
            options={
                'verbose_name': 'Footer Settings',
                'verbose_name_plural': 'Footer Settings',
            },
        ),
    ]
