/* Global styles */
.list-group-item.active {
    background-color: #198653 !important;
    border-color: #198653 !important;
}

.list-group-item.active:hover {
    background-color: #157347 !important;
    border-color: #157347 !important;
}

:root {
    --primary-color: #198653;
    --secondary-color: #188552;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

a {
    color: #198653;
    text-decoration: none;
}

a:hover {
    color: #157347;
}

.form-check-input:checked {
    background-color: #198653 !important;
    border-color: #198653 !important;
}

.text-success {
    color: #198653 !important;
}

.btn-success {
    background-color: #198653 !important;
    border-color: #198653 !important;
}

.btn-success:hover {
    background-color: #157347 !important;
    border-color: #157347 !important;
}

/* Rest of the existing styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navbar Styles */
.navbar {
    background-color: transparent;
    transition: all 0.3s ease;
    padding: 1rem 0;
    flex-direction: row;
}

.navbar.scrolled {
    background-color: rgb(255, 255, 255);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 1.5rem;
}

.logo-img {
    width: auto;
    height: 40px;
    margin-right: 10px;
}

.nav-link {
    color: #555 !important;
    font-weight: 600;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: var(--primary-color);
    color: white !important;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.nav-link.active {
    color: var(--primary-color) !important;
}

.navbar-toggler {
    border: 0;
    padding: 0.5rem;
    transition: all 0.3s ease;
    outline: none;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

@media (max-width: 991px) {
    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    .nav-link {
        padding: 0.75rem 1rem !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .nav-item:last-child .nav-link {
        border-bottom: none;
    }

    .navbar-nav {
        margin: 0 -1rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        position: fixed !important;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .navbar .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 1rem;
    }

    .navbar-brand {
        margin: 0;
        width: auto;
        order: 1;
    }

    .navbar-collapse {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background-color: white;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        max-height: calc(100vh - 70px);
        overflow-y: auto;
        order: 2;
        z-index: 1029;
    }

    .navbar-toggler {
        padding: 0.5rem;
        margin: 0;
        border: 1px solid rgba(0,0,0,.1);
        border-radius: 4px;
        order: 3;
        position: relative;
        transition: all 0.3s ease-in-out;
    }

    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        transition: all 0.3s ease-in-out;
    }

    .navbar-toggler[aria-expanded='true'] .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M6 6L24 24M6 24L24 6'/%3e%3c/svg%3e");
        transform: rotate(180deg);
    }

    .nav-link {
        padding: 1rem !important;
        border-bottom: 1px solid rgba(0,0,0,.1);
        font-size: 1.1rem;
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .dropdown-menu {
        border: none;
        background: transparent;
        padding: 0;
        margin-top: 0;
    }

    .dropdown-item {
        padding: 1rem;
        border-bottom: 1px solid rgba(0,0,0,.1);
        white-space: normal;
    }

    .dropdown-item:last-child {
        border-bottom: none;
    }

    body {
        padding-top: 70px;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.hero-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.8;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6));
    z-index: 2;
}

.hero-text {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    padding: 20px;
    max-width: 800px;
}

.hero-text h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text p {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-text .btn-success {
    font-size: 1.2rem;
    text-transform: uppercase;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.hero-text .btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Waves Animation */
.waves-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15vh;
    overflow: hidden;
    z-index: 4;
}

.waves {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 20px;
    max-height: 300px;
}

.parallax > use {
    animation: move-waves 25s cubic-bezier(.55, .5, .45, .5) infinite;
}

.parallax > use:nth-child(1) {
    animation-delay: -2s;
    animation-duration: 7s;
}

.parallax > use:nth-child(2) {
    animation-delay: -3s;
    animation-duration: 10s;
}

.parallax > use:nth-child(3) {
    animation-delay: -4s;
    animation-duration: 13s;
}

.parallax > use:nth-child(4) {
    animation-delay: -5s;
    animation-duration: 20s;
}

@keyframes move-waves {
    0% {
        transform: translate3d(-90px, 0, 0);
    }
    100% {
        transform: translate3d(85px, 0, 0);
    }
}

/* Welcome Section */
.welcome-section {
    padding: 5rem 0;
    background-color: white;
}

/* Welcome Section Image */
.welcome-section .image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.welcome-section .image-container img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 50%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.welcome-section .image-container::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    border-radius: 50%;
    opacity: 0.5;
}

.welcome-section .image-container img:hover {
    transform: scale(1.02);
}

.text-container {
    padding: 2rem;
}

.text-container h2 {
    color: var(--dark-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.text-container h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background-color: var(--primary-color);
}

.text-container p {
    color: #666;
    font-size: 1.2rem;
    line-height: 1.8;
}

/* Featured Products Section */
.deals-section {
    padding: 5rem 0;
    background-color: white;
}

.deals-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.deal-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.deal-card:hover {
    transform: translateY(-10px);
}

.deal-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.deal-content {
    padding: 1.5rem;
}

.deal-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.deal-price {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

/* Fishing Locations Section */
.fishing-explore-section {
    background-color: var(--primary-color);
    color: white;
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.fishing-explore-container {
    display: flex;
    align-items: center;
    gap: 4rem;
    position: relative;
    z-index: 2;
}

.fishing-text-content {
    flex: 1;
}

.fishing-text-content h2 {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 2rem;
}

.fishing-text-content p {
    font-size: 1.2rem;
    line-height: 1.8;
    opacity: 0.9;
}

.fishing-image-container {
    flex: 1;
    position: relative;
}

.fishing-image-container::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
}

.fishing-image-container img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
}

@media (max-width: 992px) {
    .fishing-text-content h2 {
        font-size: 2.5rem;
        text-align: center;
    }
    
    .fishing-text-content p {
        text-align: center;
    }
    
    .fishing-image-container::before {
        display: none;
    }
}

/* Video Tutorials Section */
.video-courses-section {
    padding: 5rem 0;
    background-color: var(--light-color);
}

.course-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.course-card:hover {
    transform: translateY(-10px);
}

.card-img-container {
    position: relative;
    overflow: hidden;
}

.card-img-container img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.badge-play {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.course-card:hover .badge-play {
    transform: scale(1.1);
}

/* Testimonials Section */
.testimonial-section {
    padding: 5rem 0;
    background-color: white;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin: 1rem;
}

.testimonial-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    object-fit: cover;
}

/* Promotional Video Section */
.promo-section {
    position: relative;
    padding: 5rem 0;
    background-image: url('../images/img_4.png');
    background-size: cover;
    background-position: center;
    color: white;
}

.promo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
}

.promo-play-button {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.promo-play-button:hover {
    transform: scale(1.1);
}

/* Footer Styles */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 5rem 0 2rem;
}

.footer-logo {
    height: 40px;
    margin-bottom: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

.footer-flag {
    height: 20px;
    margin-left: 10px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }

    .fishing-explore-container {
        flex-direction: column;
        text-align: center;
    }

    .fishing-text-content {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        background-color: white;
    }

    .navbar-collapse {
        background-color: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .deals-container {
        grid-template-columns: 1fr;
    }

    .welcome-section .image-container {
        margin-bottom: 2rem;
    }

    .text-container {
        text-align: center;
    }

    .text-container h2::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

/* Utility Classes */
.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.text-success {
    color: var(--primary-color) !important;
}

.bg-success {
    background-color: var(--primary-color) !important;
}

/* Solunar Page Styles */
.solunar-card {
    transition: transform 0.2s ease;
}

.solunar-card:hover {
    transform: translateY(-5px);
}

#solunar-data .card {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#solunar-data .card-body {
    padding: 1.5rem;
}

#solunar-data h5 {
    color: var(--primary-color);
    font-weight: 600;
}

#solunar-data .progress {
    border-radius: 1rem;
    background-color: #e9ecef;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

#solunar-data .progress-bar {
    border-radius: 1rem;
    transition: width 0.6s ease;
}

#date-picker, #location-select {
    border: 1px solid #ced4da;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#date-picker:focus, #location-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 134, 83, 0.25);
}

.major-period, .minor-period {
    font-family: 'Roboto Mono', monospace;
    letter-spacing: 0.5px;
}

.card-subtitle i {
    color: var(--primary-color);
}

.list-unstyled i {
    width: 20px;
    text-align: center;
}

/* Solunar Section */
.solunar-section {
    background-color: var(--light-color);
    padding: 5rem 0;
}

.solunar-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.solunar-card:hover {
    transform: translateY(-5px);
}

.moon-phase-icon {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.fishing-times {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
}

.fishing-times h6 {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.fish-icons {
    margin: 1rem 0;
}

.fish-icons i {
    font-size: 1.5rem;
    margin: 0 3px;
    transition: color 0.3s ease;
}

.fish-icons i.text-primary {
    color: var(--primary-color) !important;
}

/* County Selection Section */
.county-selection-section {
    background-color: var(--light-color);
    padding: 5rem 0;
}

.county-selection-section h2 {
    color: var(--dark-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.county-selection-section p {
    color: #666;
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto;
}

.map-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.map-link {
    display: inline-block;
    transition: transform 0.3s ease;
}

.map-link:hover {
    transform: scale(1.02);
}

.romania-map {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

@media (max-width: 768px) {
    .county-selection-section h2 {
        font-size: 2rem;
    }
    
    .county-selection-section p {
        font-size: 1.1rem;
        padding: 0 1rem;
    }
    
    .map-container {
        padding: 1rem;
    }
}
