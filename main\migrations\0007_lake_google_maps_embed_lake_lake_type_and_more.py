# Generated by Django 5.1.6 on 2025-06-05 19:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0006_alter_lake_latitude_alter_lake_longitude'),
    ]

    operations = [
        migrations.AddField(
            model_name='lake',
            name='google_maps_embed',
            field=models.TextField(blank=True, help_text="Cod iframe complet de la Google Maps (opțional). Dacă este completat, va fi folosit în locul coordonatelor. Exemplu: <iframe src='...' width='600' height='450'></iframe>", null=True, verbose_name='Cod embed Google Maps'),
        ),
        migrations.AddField(
            model_name='lake',
            name='lake_type',
            field=models.CharField(choices=[('private', 'Baltă privată'), ('public', 'Baltă publică'), ('competition', 'Baltă pentru competiții'), ('catch_release', 'Baltă cu regim "catch & release"'), ('mixed', 'Baltă cu regim mixt (reținere + catch & release)'), ('natural', 'Baltă naturală')], default='private', help_text='Selectează tipul de baltă în funcție de administrare și regulament', max_length=50, verbose_name='Tipul bălții'),
        ),
        migrations.AddField(
            model_name='lake',
            name='operating_hours',
            field=models.TextField(blank=True, help_text='Programul detaliat de funcționare al lacului (ex: Luni-Vineri: 06:00-22:00, Sâmbătă-Duminică: 05:00-23:00, Pescuit nocturn: cu acordul administratorului)', verbose_name='Program de funcționare'),
        ),
    ]
